import pandas as pd
import random as random
import string as string
import uuid

# Função para gerar IDs aleatórios
def gerar_id(tamanho=10):
    caracteres = string.ascii_uppercase + string.digits
    return ''.join(random.choices(caracteres, k=tamanho))
# Lê o arquivo Excel
df = pd.read_excel('Pasta1.xlsx')

# Gera os INSERTs
insert_statements = []

for _, row in df.iterrows():
    id_ = gerar_id()
    code = str(row['code']).strip() if row['code'] is not None else None
    name = str(row['name']).strip().replace("'", "''") if row['name'] is not None else None
    reg_num = str(row['registration_number']).strip() if row['registration_number'] is not None else None
    reg_num = f"'{reg_num}'" if pd.notnull(reg_num) else "NULL"
    type_value = str(row['type']).strip().replace("'", "''") if pd.notnull(row['type']) else None
    type_value = f"'{type_value}'" if type_value else "NULL"

    insert = f"""INSERT INTO "chemical_organic" ("id", "code", "name", "registration_number", "type") 
VALUES ('{id_}', '{code}', '{name}', {reg_num}, {type_value});"""
    insert_statements.append(insert)

# Salva em um arquivo .sql
with open('inserts_chemical_organic.sql', 'w', encoding='utf-8') as f:
    for stmt in insert_statements:
        f.write(stmt + '\n')

print("Inserts gerados com sucesso!")
