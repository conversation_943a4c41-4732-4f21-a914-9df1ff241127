from fpdf import FPDF


# Classe PDF personalizada com fundo colorido e estilo moderno
class ModernPDF(FPDF):
    def header(self):
        self.set_font('Arial', 'B', 20)
        self.set_text_color(40, 40, 40)
        self.cell(0, 15, 'Levantamento dos Endpoints', ln=True, align='C')
        self.ln(5)

    def add_section_title(self, title):
        self.set_font('Arial', 'B', 16)
        self.set_text_color(30, 30, 30)
        self.set_fill_color(230, 230, 230)
        self.cell(0, 10, title, ln=True, fill=True)
        self.ln(4)

    def add_endpoint(self, ep_title, method, params):
        # Definir cor de fundo conforme o método
        if method.startswith("GET"):
            bg_color = (204, 229, 255)  # Azul claro
            text_color = (0, 51, 102)
        elif method.startswith("POST"):
            bg_color = (204, 255, 229)  # Verde claro
            text_color = (0, 102, 51)
        elif method.startswith("DELETE"):
            bg_color = (255, 204, 204)  # Vermelho claro
            text_color = (153, 0, 0)
        elif method.startswith("PUT"):
            bg_color = (255, 229, 204)  # Laranja claro
            text_color = (153, 76, 0)
        else:
            bg_color = (240, 240, 240)
            text_color = (60, 60, 60)

        self.set_fill_color(*bg_color)
        self.set_text_color(*text_color)

        self.set_font('Arial', 'B', 12)
        self.multi_cell(0, 8, ep_title, border=1, fill=True)

        self.set_font('Arial', '', 11)
        self.multi_cell(0, 7, f'Método: {method}', border=1, fill=True)

        if params:
            self.multi_cell(0, 7, 'Parâmetros (Query-Param):', border=1, fill=True)
            for param in params:
                self.multi_cell(0, 7, f'  - {param}', border=1, fill=True)
        self.ln(4)


# Dados organizados
sections = {
    "Dados relacionados ao cliente": [
        ("Buscar cliente", "GET /clientes", ["cpfCnpj = 12332145607"]),
        ("Cadastrar cliente", "POST /clientes", []),
        ("Buscar lista de protocolos do cliente", "GET /protocolos", [
            "clienteId = 1",
            "status = PENDENTE / ABERTO / RESOLVIDO",
            "numero = 123"
        ]),
        ("Buscar protocolo específico", "GET /protocolos/{id}", []),
        ("Registrar protocolo", "POST /protocolos", [])
    ],
    "Dados relacionados à nota fiscal": [
        ("Consultar nota fiscal", "GET /notas_fiscais", [
            "numero",
            "cpfCnpj",
            "clienteId"
        ]),
        ("Consultar nota fiscal específica", "GET /notas_fiscais/{id}", [])
    ],
    "Dados relacionados ao produto": [
        ("Busca de produtos por nome e código", "GET /produtos", [
            "nome",
            "codigo"
        ])
    ],
    "Dados relacionados à localização da Assistência Técnica": [
        ("Buscar assistência técnica próxima", "GET /assistências", [
            "cep = 01001000"
        ])
    ],
    "Dados relacionados à coleta e entrega do produto": [
        ("Buscar situação de entrega/coleta do produto", "", []),
        ("Gerar código dos Correios", "", [])
    ]
}

# Criar o PDF
pdf = ModernPDF()
pdf.add_page()

# Adicionar dados
for section, endpoints in sections.items():
    pdf.add_section_title(section)
    for ep_title, method, params in endpoints:
        pdf.add_endpoint(ep_title, method, params)

# Salvar o PDF
modern_pdf_file_path = 'levantamento_endpoints_moderno.pdf'
pdf.output(modern_pdf_file_path)

modern_pdf_file_path
