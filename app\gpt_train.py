import torch
from transformers import Trainer, TrainingArguments, AutoTokenizer, AutoModelForCausalLM
from datasets import load_dataset

# Carregar o dataset
dataset = load_dataset("json", data_files="train.json", split="train")

# Carregar o tokenizer e o modelo
tokenizer = AutoTokenizer.from_pretrained("gpt2")
tokenizer.pad_token = tokenizer.eos_token  # <- Adicionado aqui
model = AutoModelForCausalLM.from_pretrained("gpt2")


# Tokenizar os dados
def tokenize_function(examples):
    tokenized_inputs = tokenizer(
        examples['text'],
        padding="max_length",
        truncation=True,
        max_length=128
    )
    tokenized_inputs["labels"] = tokenized_inputs["input_ids"].copy()
    return tokenized_inputs


tokenized_dataset = dataset.map(tokenize_function, batched=True)

# Dividir em treino e validação (opcional mas recomendado)
split_dataset = tokenized_dataset.train_test_split(test_size=0.2)
train_dataset = split_dataset["train"]
eval_dataset = split_dataset["test"]

# Definir os argumentos de treinamento
training_args = TrainingArguments(
    output_dir="./results",
    evaluation_strategy="epoch",   # Avalia a cada época
    save_strategy="epoch",         # Salva a cada época
    load_best_model_at_end=True,
    per_device_train_batch_size=4,
    per_device_eval_batch_size=4,
    num_train_epochs=5,
    logging_dir="./logs",
    logging_steps=10,
    report_to="none",  # Use "tensorboard" se quiser visualizar logs
)

# Inicializar o Trainer
trainer = Trainer(
    model=model,
    args=training_args,
    train_dataset=train_dataset,
    eval_dataset=eval_dataset
)

# Iniciar o treinamento
trainer.train()
