from cryptography.hazmat.primitives.serialization import pkcs12
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.backends import default_backend
from lxml import etree
from cryptography.hazmat.primitives import serialization
import base64

DS_NS = "http://www.w3.org/2000/09/xmldsig#"
NSMAP = {'ds': DS_NS}


def assinar_reinf(xml_path, pfx_path, pfx_password, output_path):
    # Carrega o certificado
    with open(pfx_path, 'rb') as f:
        pfx_data = f.read()
    private_key, cert, _ = pkcs12.load_key_and_certificates(pfx_data, pfx_password.encode(), default_backend())

    # Carrega e prepara o XML
    parser = etree.XMLParser(remove_blank_text=True)
    tree = etree.parse(xml_path, parser)
    root = tree.getroot()

    # Define o namespace usado no XML
    ns_reinf = {'r': 'http://www.esocial.gov.br/schema/evt/evtRemun/02_00_00'}

    # Encontra o elemento evtRemun no XML usando o namespace
    evt = root.find('.//r:evento/r:evtRemun', namespaces=ns_reinf)

    # Se não encontrar o evento, levanta um erro
    if evt is None:
        raise ValueError("O elemento 'evtRemun' não foi encontrado no XML.")

    # Como não há atributo 'id', criamos um identificador único com base no conteúdo do XML
    evt_id = f"evt_{hash(evt)}"  # Gerando um identificador único baseado no hash do evento

    # Canonicaliza o nó evtRemun
    evento_c14n = etree.tostring(evt, method="c14n", exclusive=True, with_comments=False)

    # Recalcula digest sobre o evento
    from hashlib import sha256
    digest_value = base64.b64encode(sha256(evento_c14n).digest()).decode()

    # Monta SignedInfo
    signed_info = etree.Element(etree.QName(DS_NS, "SignedInfo"), nsmap=NSMAP)

    canon_method = etree.SubElement(signed_info, etree.QName(DS_NS, "CanonicalizationMethod"))
    canon_method.attrib["Algorithm"] = "http://www.w3.org/TR/2001/REC-xml-c14n-20010315"

    sig_method = etree.SubElement(signed_info, etree.QName(DS_NS, "SignatureMethod"))
    sig_method.attrib["Algorithm"] = "http://www.w3.org/2001/04/xmldsig-more#rsa-sha256"

    reference = etree.SubElement(signed_info, etree.QName(DS_NS, "Reference"))
    reference.attrib["URI"] = f"#{evt_id}"

    transforms = etree.SubElement(reference, etree.QName(DS_NS, "Transforms"))
    transform1 = etree.SubElement(transforms, etree.QName(DS_NS, "Transform"))
    transform1.attrib["Algorithm"] = "http://www.w3.org/2000/09/xmldsig#enveloped-signature"
    transform2 = etree.SubElement(transforms, etree.QName(DS_NS, "Transform"))
    transform2.attrib["Algorithm"] = "http://www.w3.org/TR/2001/REC-xml-c14n-20010315"

    digest_method = etree.SubElement(reference, etree.QName(DS_NS, "DigestMethod"))
    digest_method.attrib["Algorithm"] = "http://www.w3.org/2001/04/xmlenc#sha256"

    digest_val = etree.SubElement(reference, etree.QName(DS_NS, "DigestValue"))
    digest_val.text = digest_value

    # Canonicaliza SignedInfo
    signed_info_c14n = etree.tostring(signed_info, method="c14n", exclusive=True)

    # Assina o SignedInfo
    signature = private_key.sign(
        data=signed_info_c14n,
        padding=padding.PKCS1v15(),
        algorithm=hashes.SHA256()
    )
    signature_value_b64 = base64.b64encode(signature).decode()

    # Monta a assinatura (Signature)
    signature_el = etree.Element(etree.QName(DS_NS, "Signature"), nsmap=NSMAP)
    signature_el.append(signed_info)

    sig_val = etree.SubElement(signature_el, etree.QName(DS_NS, "SignatureValue"))
    sig_val.text = signature_value_b64

    key_info = etree.SubElement(signature_el, etree.QName(DS_NS, "KeyInfo"))
    x509_data = etree.SubElement(key_info, etree.QName(DS_NS, "X509Data"))
    x509_cert = etree.SubElement(x509_data, etree.QName(DS_NS, "X509Certificate"))

    # Insere o certificado sem cabeçalho/rodapé
    cert_der = cert.public_bytes(serialization.Encoding.DER)
    cert_b64 = base64.b64encode(cert_der).decode()
    x509_cert.text = cert_b64

    # Insere a assinatura após o evento
    reinf_evento = evt.getparent()  # Acha o elemento pai de 'evtRemun' (evento)
    reinf_evento.append(signature_el)

    # Salva o XML assinado
    tree.write(output_path, encoding="utf-8", xml_declaration=True, pretty_print=True)
    print(f"Arquivo assinado salvo em: {output_path}")


# Chamada para a função de assinatura
assinar_reinf(
    xml_path="reinf_original.xml",
    pfx_path="federal_cert.pfx",
    pfx_password="Federallcom13",
    output_path="reinf_assinado.xml"
)
