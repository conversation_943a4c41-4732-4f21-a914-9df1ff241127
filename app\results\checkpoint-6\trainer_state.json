{"best_metric": 1.787015438079834, "best_model_checkpoint": "./results\\checkpoint-6", "epoch": 2.0, "eval_steps": 500, "global_step": 6, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 1.0, "eval_loss": 1.9533133506774902, "eval_runtime": 0.8728, "eval_samples_per_second": 4.583, "eval_steps_per_second": 1.146, "step": 3}, {"epoch": 2.0, "eval_loss": 1.787015438079834, "eval_runtime": 0.909, "eval_samples_per_second": 4.4, "eval_steps_per_second": 1.1, "step": 6}], "logging_steps": 10, "max_steps": 15, "num_input_tokens_seen": 0, "num_train_epochs": 5, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 1567752192000.0, "train_batch_size": 4, "trial_name": null, "trial_params": null}