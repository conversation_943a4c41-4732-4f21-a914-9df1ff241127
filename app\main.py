from app.retrieval import RetrievalEngine
from app.generation import ResponseGenerator

documents = open("train.json", encoding="utf-8").read().splitlines()

retrieval_engine = RetrievalEngine()
retrieval_engine.index_documents(documents)

response_generator = ResponseGenerator()

print("Sistema pronto para responder. Digite 'sair' para encerrar.")

while True:
    query = input("Digite sua pergunta: ")

    if query.lower() == "sair":
        print("Saindo...")
        break

    retrieved_docs = retrieval_engine.search(query)

    context = "\n".join(retrieved_docs)
    response = response_generator.generate_response(context, query)

    print("\nResposta gerada:")
    print(response)
    print("-" * 50)
