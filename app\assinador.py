import xml.etree.ElementTree as ET
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.backends import default_backend
from cryptography.x509 import load_pem_x509_certificate
from signxml import XMLSigner, methods

# Função para carregar o certificado X.509
def carregar_certificado(cert_path):
    with open(cert_path, "rb") as cert_file:
        cert = cert_file.read()
    return load_pem_x509_certificate(cert, default_backend())

# Função para carregar a chave privada
def carregar_chave_privada(key_path, senha=None):
    with open(key_path, "rb") as key_file:
        # Se a chave não tiver senha, passar None
        private_key = serialization.load_pem_private_key(key_file.read(), password=senha, backend=default_backend())
    return private_key

# Função de assinatura
def assinar_xml(xml_string, cert_path, key_path, senha=None):
    # Carregar o certificado e a chave privada
    certificate = carregar_certificado(cert_path)
    private_key = carregar_chave_privada(key_path, senha)

    # Criar a cadeia de certificados (aqui estamos usando só o certificado fornecido)
    cert_chain = [certificate]

    # Criar o objeto XMLSigner
    signer = XMLSigner(method=methods.enveloped, signature_algorithm='rsa-sha256')

    # Converter a string XML para um objeto XML Element
    root = ET.fromstring(xml_string)  # Parse a string XML para um Element

    # Assinar o XML com o certificado e chave privada
    signed_root = signer.sign(root, key=private_key, cert=cert_chain)

    # Retornar o XML assinado como string
    return ET.tostring(signed_root, encoding='utf-8').decode('utf-8')

# Exemplo de uso
xml_string = """<?xml version="1.0" encoding="UTF-8"?>
<Reinf xmlns="http://www.reinf.esocial.gov.br/schemas/envioLoteEventosAssincrono/v1_00_00">
  <envioLoteEventos>
    <ideContribuinte>
      <tpInsc>1</tpInsc>
      <nrInsc>00914803</nrInsc>
    </ideContribuinte>
    <eventos>
      <evento xmlns="http://www.reinf.esocial.gov.br/schemas/evtPrestadorServicos/v2_01_02" Id="ID2514271020015242025050912313100014">
        <evtServPrest id="ID2514271020015242025050912313100014">
          <ideEvento>
            <indRetif>1</indRetif>
            <perApur>2025-04</perApur>
            <tpAmb>1</tpAmb>
            <procEmi>1</procEmi>
            <verProc>REINF.Satellite</verProc>
          </ideEvento>
          <ideContri>
            <tpInsc>1</tpInsc>
            <nrInsc>00914803</nrInsc>
          </ideContri>
          <infoServPrest>
            <ideEstabPrest>
              <tpInscEstabPrest>1</tpInscEstabPrest>
              <nrInscEstabPrest>00914803000151</nrInscEstabPrest>
              <ideTomador>
                <tpInscTomador>1</tpInscTomador>
                <nrInscTomador>51427102001524</nrInscTomador>
                <indObra>0</indObra>
                <vlrTotalBruto>339885.43</vlrTotalBruto>
                <vlrTotalBaseRet>339885.43</vlrTotalBaseRet>
                <vlrTotalRetPrinc>37387.40</vlrTotalRetPrinc>
                <vlrTotalRetAdic>0.00</vlrTotalRetAdic>
                <vlrTotalNRetPrinc>0.00</vlrTotalNRetPrinc>
                <vlrTotalNRetAdic>0.00</vlrTotalNRetAdic>
                <nfs>
                  <serie>RPS</serie>
                  <numDocto>26917</numDocto>
                  <dtEmissaoNF>2025-04-01</dtEmissaoNF>
                  <vlrBruto>337488.97</vlrBruto>
                  <infoTpServ>
                    <tpServico>100000002</tpServico>
                    <vlrBaseRet>337488.97</vlrBaseRet>
                    <vlrRetencao>37123.79</vlrRetencao>
                  </infoTpServ>
                </nfs>
                <nfs>
                  <serie>RPS</serie>
                  <numDocto>26918</numDocto>
                  <dtEmissaoNF>2025-04-01</dtEmissaoNF>
                  <vlrBruto>2396.46</vlrBruto>
                  <infoTpServ>
                    <tpServico>100000002</tpServico>
                    <vlrBaseRet>2396.46</vlrBaseRet>
                    <vlrRetencao>263.61</vlrRetencao>
                  </infoTpServ>
                </nfs>
              </ideTomador>
            </ideEstabPrest>
          </infoServPrest>
        </evtServPrest>
      </evento>
    </eventos>
  </envioLoteEventos>
</Reinf>"""  # Substitua pelo seu XML real
cert_path = "certificado.pem"  # Caminho do seu certificado X.509
key_path = "chave.pem"  # Caminho da sua chave privada (PEM)

xml_assinado = assinar_xml(xml_string, cert_path, key_path)
print(xml_assinado)
