from transformers import AutoModelForCausalLM, AutoTokenizer
import torch

model_path = "results/checkpoint-15"  # Substitua pelo caminho do melhor checkpoint salvo
tokenizer = AutoTokenizer.from_pretrained("gpt2")
model = AutoModelForCausalLM.from_pretrained(model_path)

while True:
    prompt = input("Digite sua pergunta: ")
    inputs = tokenizer(prompt, return_tensors="pt")
    outputs = model.generate(**inputs, max_length=100, do_sample=False)  # Mude para do_sample=False

    print(tokenizer.decode(outputs[0], skip_special_tokens=True))
