from transformers import AutoTokenizer, AutoModelForQuestionAnswering
import torch

class ResponseGenerator:
    def __init__(self, model_name="pierreguillou/bert-base-cased-squad-v1.1-portuguese"):
        print("Carregando modelo QA em português...")
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModelForQuestionAnswering.from_pretrained(model_name)
        print("Modelo carregado.")

    def generate_response(self, context, question, max_length=512):
        # <PERSON><PERSON><PERSON><PERSON> que o tamanho máximo da entrada não ultrapasse o limite do modelo
        inputs = self.tokenizer(question, context, return_tensors="pt", truncation=True, max_length=max_length)

        with torch.no_grad():
            outputs = self.model(**inputs)

        start = torch.argmax(outputs.start_logits)
        end = torch.argmax(outputs.end_logits) + 1

        answer_ids = inputs["input_ids"][0][start:end]
        answer = self.tokenizer.decode(answer_ids, skip_special_tokens=True)

        return answer
