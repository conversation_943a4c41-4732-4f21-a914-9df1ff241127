import requests

# Lista de produtos no formato (ID, Nome)
produtos = [
    (76, "Fone de Ouvido AHP 2401"),
    (77, "Caixa Amplificada ACA 480 VIPER II"),
    (78, "Party Vox ACA 2000"),
    (79, "Caixa Amplificada ACA 800 GLADIADOR Amvox")
]


# URL do endpoint (substitua pela sua real)
endpoint_url = "https://amvox-api.aplopes.com/produtos"

# Loop para enviar os produtos
for prod_id, nome in produtos:
    payload = {
        "nome": nome,
        "codigo": f"PROD-{prod_id}",
        "descricao": f"Produto: {nome}"
    }

    response = requests.post(endpoint_url, json=payload)

    if response.status_code == 201:
        print(f"[OK] Produto {prod_id} enviado com sucesso.")
    else:
        print(f"[ERRO] Produto {prod_id} falhou ({response.status_code}): {response.text}")
