from transformers import AutoModelForCausalLM, AutoTokenizer
from peft import LoraConfig, get_peft_model
import torch

def train_lora(model_name, output_dir="lora-model"):
    print("🚀 Iniciando treinamento LoRA com o modelo:", model_name)
    # Carregar modelo com suporte a INT8 (quantização de 8 bits)
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        device_map="auto"   # Distribui o modelo automaticamente entre dispositivos
    )

    print("Distribuiu o modelo")

    # Carregar o tokenizer
    # Carregar o tokenizer
    tokenizer = AutoTokenizer.from_pretrained(model_name)

    # Definir pad_token manualmente (Falcon não tem por padrão)
    tokenizer.pad_token = tokenizer.eos_token

    print("carregou o tokenizador")

    # Configurar LoRA
    lora_config = LoraConfig(
        r=8,                         # Rank do LoRA
        lora_alpha=32,               # Escala dos pesos
        target_modules=["query_key_value"],  # Camadas a serem ajustadas
        lora_dropout=0.1,            # Dropout para evitar overfitting
        bias="none",                 # Não ajustar bias
        task_type="CAUSAL_LM"        # Tipo de tarefa: linguagem causal
    )

    print("carregou a configuração")

    # Aplicar LoRA ao modelo
    model = get_peft_model(model, lora_config)

    print("Aplicou ao modelo")

    # Exibir as camadas LoRA configuradas
    print("Camadas LoRA configuradas:")
    model.print_trainable_parameters()

    # Pré-processar os textos de entrada
    texts = [
        """Você é um Assistente Pessoal especializado.

[Usuário]: Olá, meu número é 11999998888  
[Assistente]: Olá! Que bom te ver por aqui. Só um momento enquanto verifico seus dados...""",

        """Você é um Assistente Pessoal especializado.

[Usuário]: Quero ver meus últimos pedidos.  
[Assistente]: Claro! Buscando seus pedidos mais recentes agora...""",

        """Você é um Assistente Pessoal especializado.

[Usuário]: Me mostra o nome do produto do pedido #1234  
[Assistente]: Claro! Vou buscar as informações do pedido #1234 agora mesmo.""",

        """Você é um Assistente Pessoal especializado. 

[Usuário]: Qual é o telefone cadastrado no sistema?  
[Assistente]: Um instante... Vou verificar seus dados no sistema de clientes."""
    ]
    inputs = tokenizer(texts, return_tensors="pt", padding=True, truncation=True)
    labels = inputs["input_ids"]

    # Mover dados para o dispositivo correto (GPU, se disponível)
    inputs = {key: value.to(model.device) for key, value in inputs.items()}
    labels = labels.to(model.device)

    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4)

    for epoch in range(3):  # Número de épocas
        model.train()
        outputs = model(**inputs, labels=labels)
        loss = outputs.loss
        loss.backward()
        optimizer.step()
        optimizer.zero_grad()

        print(f"Epoch {epoch + 1}, Loss: {loss.item()}")
    model.save_pretrained(output_dir)
    tokenizer.save_pretrained(output_dir)
    print(f"Modelo salvo em '{output_dir}'")

if __name__ == "__main__":
    train_lora(model_name="tiiuae/falcon-rw-1b")

