{"best_metric": 1.6016453504562378, "best_model_checkpoint": "./results\\checkpoint-12", "epoch": 4.0, "eval_steps": 500, "global_step": 12, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 1.0, "eval_loss": 1.9533133506774902, "eval_runtime": 0.8728, "eval_samples_per_second": 4.583, "eval_steps_per_second": 1.146, "step": 3}, {"epoch": 2.0, "eval_loss": 1.787015438079834, "eval_runtime": 0.909, "eval_samples_per_second": 4.4, "eval_steps_per_second": 1.1, "step": 6}, {"epoch": 3.0, "eval_loss": 1.6674193143844604, "eval_runtime": 0.874, "eval_samples_per_second": 4.577, "eval_steps_per_second": 1.144, "step": 9}, {"epoch": 3.3333333333333335, "grad_norm": 7.494251251220703, "learning_rate": 1.6666666666666667e-05, "loss": 2.8325, "step": 10}, {"epoch": 4.0, "eval_loss": 1.6016453504562378, "eval_runtime": 0.8736, "eval_samples_per_second": 4.579, "eval_steps_per_second": 1.145, "step": 12}], "logging_steps": 10, "max_steps": 15, "num_input_tokens_seen": 0, "num_train_epochs": 5, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 3135504384000.0, "train_batch_size": 4, "trial_name": null, "trial_params": null}